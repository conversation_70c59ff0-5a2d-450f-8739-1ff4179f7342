<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Track your progress through YouTube playlists with IsotopeAI YT Tracker. Perfect for online courses, tutorials, and educational content. Resume where you left off and never lose your place.">
    <meta name="keywords" content="YouTube playlist tracker, video progress tracking, online course tracker, tutorial progress, educational videos, IsotopeAI">
    <meta name="author" content="IsotopeAI">
    <title>YouTube Playlist Tracker - IsotopeAI YT Tracker</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-left">
                    <h1 class="app-title">
                        <i class="fab fa-youtube"></i>
                        IsotopeAI - YT Tracker
                    </h1>
                    <div class="playlist-info">
                        <span id="currentPlaylistTitle">No playlist loaded</span>
                        <span id="globalStats">0/0 videos • 0% complete</span>
                    </div>
                    
                </div>
                <div class="header-right">
                    <a href="dashboard.html" class="btn btn--outline btn--sm">
                        <i class="fas fa-arrow-left"></i> Dashboard
                    </a>
                    <button class="btn btn--outline btn--sm" id="settingsBtn">
                        <i class="fas fa-cog"></i> Settings
                    </button>
                </div>
            </div>
        </header>

        <!-- No Playlist State -->
        <div class="upload-section" id="uploadSection">
            <div class="card upload-card">
                <div class="card__body">
                    <h2>No Playlist Selected</h2>
                    <p class="upload-description">Go to the dashboard to add and select a playlist to start tracking your progress</p>
                    <div class="empty-state-actions">
                        <a href="dashboard.html" class="btn btn--primary btn--full-width">
                            <i class="fas fa-chart-bar"></i> Go to Dashboard
                        </a>
                    </div>
                    <div class="divider">
                        <span>or</span>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Quick Start - Load Playlist URL</label>
                        <input
                            type="url"
                            id="playlistUrlInput"
                            class="form-control"
                            placeholder="https://www.youtube.com/playlist?list=..."
                        >
                        <div class="url-validation" id="urlValidation"></div>
                    </div>
                    <button class="btn btn--outline btn--full-width" id="loadPlaylistBtn">
                        <i class="fas fa-upload"></i> Load Playlist
                    </button>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="main-content" id="mainContent" style="display: none;">
            <!-- Sidebar - Video List -->
            <aside class="sidebar sidebar-left">
                <div class="sidebar-header">
                    <h3>Playlist Videos</h3>
                    <div class="playlist-controls">
                        <input type="text" id="videoSearchInput" class="form-control" placeholder="Search videos...">
                        <div class="filter-buttons">
                            <button class="btn btn--sm filter-btn active" data-filter="all">All</button>
                            <button class="btn btn--sm filter-btn" data-filter="completed">Completed</button>
                            <button class="btn btn--sm filter-btn" data-filter="progress">In Progress</button>
                            <button class="btn btn--sm filter-btn" data-filter="unwatched">Unwatched</button>
                        </div>
                    </div>
                </div>
                <div class="video-list" id="videoList">
                    <!-- Videos will be populated here -->
                </div>
            </aside>

            <!-- Center - Video Player -->
            <section class="video-player-section">
                <div class="video-player-container">
                    <div class="video-player" id="videoPlayer">
                        <div class="video-placeholder">
                            <i class="fab fa-youtube"></i>
                            <p>Select a video to start playing</p>
                        </div>
                    </div>
                    <div class="video-controls">
                        <div class="control-row">
                            <button class="btn btn--primary" id="playPauseBtn">
                                <i class="fas fa-play"></i>
                            </button>
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div class="progress-bar-fill" id="progressBarFill"></div>
                                    <div class="progress-handle" id="progressHandle"></div>
                                </div>
                                <div class="time-display">
                                    <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
                                </div>
                            </div>
                            <button class="btn btn--secondary" id="markCompleteBtn">
                                <i class="fas fa-check"></i> Mark Complete
                            </button>
                        </div>
                    </div>
                    <div class="video-info">
                        <h3 id="currentVideoTitle">No video selected</h3>
                        <div class="video-meta">
                            <span id="videoDuration">Duration: --:--</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Right Sidebar - Analytics -->
            <aside class="sidebar sidebar-right">
                <div class="analytics-panel">
                    <!-- Progress Overview -->
                    <div class="card analytics-card">
                        <div class="card__header">
                            <h4>Progress Overview</h4>
                        </div>
                        <div class="card__body">
                            <div class="progress-overview">
                                <div class="circular-progress" id="overallProgress">
                                    <div class="progress-circle">
                                        <div class="progress-text">0%</div>
                                    </div>
                                </div>
                                <div class="progress-stats">
                                    <div class="stat-item">
                                        <span class="stat-label"><i class="fas fa-check-circle" aria-hidden="true"></i> Completed</span>
                                        <span class="stat-value" id="completedCount">0/0</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label"><i class="fas fa-clock" aria-hidden="true"></i> Time Watched</span>
                                        <span class="stat-value" id="timeWatched">0h 0m</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label"><i class="fas fa-fire" aria-hidden="true"></i> Streak</span>
                                        <span class="stat-value" id="currentStreak">0 days</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </aside>
        </main>

        <!-- Settings Modal -->
        <div class="modal hidden" id="settingsModal">
            <div class="modal-overlay" id="modalOverlay"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Settings</h3>
                    <button class="modal-close" id="closeSettingsBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="settings-section">
                        <h4>Appearance</h4>
                        <div class="setting-item">
                            <label class="form-label">Theme</label>
                            <select id="themeSelect" class="form-control">
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                                <option value="auto">Auto</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h4>Tracking</h4>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoSaveToggle" checked>
                                <span class="checkmark"></span>
                                Auto-save progress
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="autoCompleteToggle" checked>
                                <span class="checkmark"></span>
                                Auto-mark videos as complete
                            </label>
                        </div>
                        <div class="setting-item">
                            <label class="checkbox-label">
                                <input type="checkbox" id="notificationsToggle" checked>
                                <span class="checkmark"></span>
                                Enable notifications
                            </label>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h4>Data</h4>
                        <div class="setting-actions">
                            <button class="btn btn--outline" id="exportDataBtn">
                                <i class="fas fa-download"></i> Export Data
                            </button>
                            <button class="btn btn--outline" id="importDataBtn">
                                <i class="fas fa-upload"></i> Import Data
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn--secondary" id="resetSettingsBtn">Reset to Defaults</button>
                    <button class="btn btn--primary" id="saveSettingsBtn">Save Settings</button>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <span>&copy; 2025 IsotopeAI - YT Tracker. An addon to <a href="https://isotopeai.in" target="_blank">isotopeai.in</a> for enhanced productivity</span>
                <a href="mailto:<EMAIL>" class="footer-contact-link">Contact</a>
            </div>
        </footer>
    </div>

    <!-- Loading indicator -->
    <div id="loadingIndicator" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading playlist...</p>
        </div>
    </div>

    <!-- Toast notifications -->
    <div id="toastContainer" class="toast-container"></div>



    <script src="config.js"></script>
    <script src="storage.js"></script>
    <script src="api.js"></script>
    <script src="app.js"></script>
</body>
</html>
